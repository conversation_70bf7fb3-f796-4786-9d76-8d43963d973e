import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeContext } from '../context/ThemeContext';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import Footer from '../components/Footer';
import CookieConsent from '../components/CookieConsent';
import logoLight from '../assets/text-only-modern.svg';
import logoDark from '../assets/text-only-modern-dark.svg';
import '../styles/landing.css';

const LandingPage = () => {
  const { darkMode, toggleTheme } = useContext(ThemeContext);
  const navigate = useNavigate();

  const handleNavigate = (path) => {
    navigate(`/${path}`);
  };

  return (
    <div className={`app ${darkMode ? 'dark-mode' : 'light-mode'}`}>
      <div className="theme-toggle">
        <button onClick={toggleTheme} className="theme-button">
          {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
        </button>
      </div>

      <div className="logo-container">
        <img
          src={darkMode ? logoDark : logoLight}
          alt="The Grin Bin"
          className="main-logo"
        />
      </div>

      <div className="landing-container">
        <h1 className="landing-title">Welcome to The Grin Bin</h1>
        <p className="landing-subtitle">Choose your entertainment adventure</p>

        <div className="section-cards">
          <div className="section-card" onClick={() => handleNavigate('wordplay')}>
            <div className="card-icon">🎭</div>
            <h2>Word Play</h2>
            <p>Discover jokes, facts, riddles, and trivia that will entertain and challenge your mind</p>
            <div className="card-features">
              <span>🎭 Dad Jokes</span>
              <span>🧠 Random Facts</span>
              <span>🤔 Riddles</span>
              <span>🎯 Trivia</span>
            </div>
            <button className="card-button">Explore Word Play</button>
          </div>

          <div className="section-card" onClick={() => handleNavigate('classic-games')}>
            <div className="card-icon">🎮</div>
            <h2>Classic Games</h2>
            <p>Enjoy timeless games that have entertained generations of players</p>
            <div className="card-features">
              <span>🎲 Coming Soon</span>
              <span>🃏 Card Games</span>
              <span>🧩 Puzzles</span>
              <span>🎯 Arcade</span>
            </div>
            <button className="card-button">Explore Classic Games</button>
          </div>
        </div>
      </div>

      <Footer onNavigate={handleNavigate} />
      <CookieConsent />
    </div>
  );
};

export default LandingPage;
