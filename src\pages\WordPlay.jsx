import React, { useEffect, useState, useContext, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeContext } from '../context/ThemeContext';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import JokeCard from '../components/JokeCard';
import FactCard from '../components/FactCard';
import RiddleCard from '../components/RiddleCard';
import TriviaCard from '../components/TriviaCard';
import Notepad from '../components/Notepad';
import Footer from '../components/Footer';
import CookieConsent from '../components/CookieConsent';
import logoLight from '../assets/text-only-modern.svg';
import logoDark from '../assets/text-only-modern-dark.svg';
import RefreshIcon from '@mui/icons-material/Refresh';
import { preconnect, prefetchDNS } from 'react-dom';
import SubmissionForm from '../components/SubmissionForm';
import VotingButtons from '../components/VotingButtons';
import { fetchApprovedContent } from '../services/firebase';
import CloseIcon from '@mui/icons-material/Close';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import PeopleOutlineIcon from '@mui/icons-material/PeopleOutline';
import '../styles/app.css';

const WordPlay = () => {
  const { darkMode, toggleTheme } = useContext(ThemeContext);
  const navigate = useNavigate();

  const [category, setCategory] = useState('joke'); // 'joke', 'fact', 'riddle', 'trivia'
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Add trivia-specific state
  const [triviaQuestion, setTriviaQuestion] = useState('');
  const [triviaCorrectAnswer, setTriviaCorrectAnswer] = useState('');
  const [triviaIncorrectAnswers, setTriviaIncorrectAnswers] = useState([]);

  // Add riddle-specific state
  const [riddleQuestion, setRiddleQuestion] = useState('');
  const [riddleAnswer, setRiddleAnswer] = useState('');

  // Available joke categories
  const jokeCategories = [
    { id: 'any', name: 'Any' },
    { id: 'programming', name: 'Programming' },
    { id: 'misc', name: 'Misc' },
    { id: 'pun', name: 'Pun' },
    { id: 'dark', name: 'Dark' },
    { id: 'dad', name: 'Dad' }
  ];

  const clearJokes = () => {
    setSavedJokes([]);
  };

  const clearFacts = () => {
    setSavedFacts([]);
  };

  // Initialize saved jokes and facts from localStorage or empty arrays
  const [savedJokes, setSavedJokes] = useState(() => {
    const saved = localStorage.getItem('savedJokes');
    return saved ? JSON.parse(saved) : [];
  });
  const [savedFacts, setSavedFacts] = useState(() => {
    const saved = localStorage.getItem('savedFacts');
    return saved ? JSON.parse(saved) : [];
  });

  // Add state for saved trivia
  const [savedTrivia, setSavedTrivia] = useState(() => {
    const saved = localStorage.getItem('savedTrivia');
    return saved ? JSON.parse(saved) : [];
  });

  // Add function to clear trivia
  const clearTrivia = () => {
    setSavedTrivia([]);
  };

  // Add state for saved riddles
  const [savedRiddles, setSavedRiddles] = useState(() => {
    const saved = localStorage.getItem('savedRiddles');
    return saved ? JSON.parse(saved) : [];
  });

  // Add function to clear riddles
  const clearRiddles = () => {
    setSavedRiddles([]);
  };

  const [notepadTab, setNotepadTab] = useState('joke'); // 'joke', 'fact', or 'trivia'

  // Add a new state for dropdown visibility
  const [jokeDropdownOpen, setJokeDropdownOpen] = useState(false);
  // Add a ref for the dropdown
  const jokeDropdownRef = useRef(null);

  // Add a click outside handler to close the dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (jokeDropdownRef.current && !jokeDropdownRef.current.contains(event.target)) {
        setJokeDropdownOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Preload resources when the app loads
  useEffect(() => {
    // Preconnect to APIs we'll use
    preconnect('https://icanhazdadjoke.com');
    preconnect('https://uselessfacts.jsph.pl');
    preconnect('https://the-trivia-api.com');

    // Prefetch DNS for backup APIs
    prefetchDNS('https://api.chucknorris.io');
    prefetchDNS('https://opentdb.com');
  }, []);

  const fetchContent = async (retryCount = 0, fallbackAttempt = 0) => {
    setLoading(true);
    setError(null);

    try {
      let url = '';
      let headers = {};

      if (category === 'joke') {
        // Always use icanhazdadjoke API for dad jokes
        url = 'https://icanhazdadjoke.com/';
        headers = { Accept: 'application/json' };
      } else if (category === 'fact') {
        url = 'https://uselessfacts.jsph.pl/random.json?language=en';
      } else if (category === 'riddle') {
        // For riddles, set the states directly before fetching to prevent flashing
        setRiddleQuestion('');
        setRiddleAnswer('');
        url = 'https://riddles-api.vercel.app/random';
      } else if (category === 'trivia') {
        url = 'https://the-trivia-api.com/api/questions?limit=1';
      }

      // Add timeout to fetch to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const res = await fetch(url, {
        headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!res.ok) {
        throw new Error(`API responded with status: ${res.status}`);
      }

      const data = await res.json();

      if (category === 'joke') {
        setContent(data.joke);
      } else if (category === 'fact') {
        setContent(data.text);
      } else if (category === 'riddle') {
        // Check if riddle is too long (over 300 characters)
        if (data.riddle.length > 300) {
          console.log('Riddle too long, fetching another one...');
          // Retry with a new riddle if this one is too long
          setLoading(false);
          return fetchContent(retryCount, fallbackAttempt);
        }

        setRiddleQuestion(data.riddle);
        setRiddleAnswer(data.answer);
        setContent(`Q: ${data.riddle} A: ${data.answer}`);
      } else if (category === 'trivia') {
        if (data && data.length > 0) { // For The Trivia API
          const triviaData = data[0];
          setTriviaQuestion(triviaData.question);
          setTriviaCorrectAnswer(triviaData.correctAnswer);
          setTriviaIncorrectAnswers(triviaData.incorrectAnswers);
          setContent(`Q: ${triviaData.question} A: ${triviaData.correctAnswer}`);
        } else {
          throw new Error('Failed to fetch trivia');
        }
      }
    } catch (err) {
      console.error('Fetch error:', err);

      // Still implement retry logic
      if (retryCount < 2) {
        console.log(`Retrying fetch (attempt ${retryCount + 1})...`);
        setLoading(false);
        return fetchContent(retryCount + 1);
      }

      // Set error for internal tracking but don't display it
      setError(err.message);

      // If all retries fail, just show the loading spinner until user tries again
      // or reset content to empty to indicate something went wrong without an error message
      if (category === 'trivia') {
        setTriviaQuestion('');
        setTriviaCorrectAnswer('');
        setTriviaIncorrectAnswers([]);
      }
      setContent('');
    } finally {
      setLoading(false);
    }
  };

  // Add a useEffect to reset content when category changes
  useEffect(() => {
    // Clear content when category changes to trigger animation
    setContent('');

    // Reset all category-specific states to ensure clean transitions
    if (category === 'trivia') {
      setTriviaQuestion('');
      setTriviaCorrectAnswer('');
      setTriviaIncorrectAnswers([]);
      // Preconnect to trivia API
      preconnect('https://the-trivia-api.com');
    } else if (category === 'joke') {
      // Preconnect to joke API
      preconnect('https://icanhazdadjoke.com');
    } else if (category === 'fact') {
      // Preconnect to fact API
      preconnect('https://uselessfacts.jsph.pl');
    } else if (category === 'riddle') {
      setRiddleQuestion('');
      setRiddleAnswer('');
      preconnect('https://riddles-api.vercel.app');
    }

    // Add a small delay before fetching new content to allow for smooth transition
    const timer = setTimeout(() => {
      fetchContent();
    }, 100);

    return () => clearTimeout(timer);
  }, [category]);

  // Save current content to notepad (jokes or facts)
  const saveToNotepad = () => {
    if (!content) return;
    if (category === 'joke' && !savedJokes.includes(content)) {
      setSavedJokes([...savedJokes, content]);
    } else if (category === 'fact' && !savedFacts.includes(content)) {
      setSavedFacts([...savedFacts, content]);
    } else if (category === 'riddle' && !savedRiddles.includes(content)) {
      setSavedRiddles([...savedRiddles, content]);
    } else if (category === 'trivia' && !savedTrivia.includes(content)) {
      setSavedTrivia([...savedTrivia, content]);
    }
  };

  // Persist saved jokes in localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('savedJokes', JSON.stringify(savedJokes));
  }, [savedJokes]);

  // Persist saved facts in localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('savedFacts', JSON.stringify(savedFacts));
  }, [savedFacts]);

  // Add useEffect to persist saved trivia in localStorage
  useEffect(() => {
    localStorage.setItem('savedTrivia', JSON.stringify(savedTrivia));
  }, [savedTrivia]);

  // Add useEffect to persist saved riddles in localStorage
  useEffect(() => {
    localStorage.setItem('savedRiddles', JSON.stringify(savedRiddles));
  }, [savedRiddles]);

  // Copy all saved items from the current notepad tab
  const copyNotepad = () => {
    let textToCopy = '';

    if (notepadTab === 'joke') {
      textToCopy = savedJokes.join('\n\n');
    } else if (notepadTab === 'fact') {
      textToCopy = savedFacts.join('\n\n');
    } else if (notepadTab === 'trivia') {
      textToCopy = savedTrivia.join('\n\n');
    } else if (notepadTab === 'riddle') {
      textToCopy = savedRiddles.join('\n\n');
    }

    navigator.clipboard.writeText(textToCopy);
  };

  // Add these new functions to handle item removal and undo
  const removeItem = (tab, index) => {
    if (tab === 'joke') {
      const newJokes = [...savedJokes];
      newJokes.splice(index, 1);
      setSavedJokes(newJokes);
    } else if (tab === 'fact') {
      const newFacts = [...savedFacts];
      newFacts.splice(index, 1);
      setSavedFacts(newFacts);
    } else if (tab === 'riddle') {
      const newRiddles = [...savedRiddles];
      newRiddles.splice(index, 1);
      setSavedRiddles(newRiddles);
    } else if (tab === 'trivia') {
      const newTrivia = [...savedTrivia];
      newTrivia.splice(index, 1);
      setSavedTrivia(newTrivia);
    }
  };

  const undoRemove = (tab, item, index) => {
    if (tab === 'joke') {
      const newJokes = [...savedJokes];
      newJokes.splice(index, 0, item);
      setSavedJokes(newJokes);
    } else if (tab === 'fact') {
      const newFacts = [...savedFacts];
      newFacts.splice(index, 0, item);
      setSavedFacts(newFacts);
    } else if (tab === 'riddle') {
      const newRiddles = [...savedRiddles];
      newRiddles.splice(index, 0, item);
      setSavedRiddles(newRiddles);
    } else if (tab === 'trivia') {
      const newTrivia = [...savedTrivia];
      newTrivia.splice(index, 0, item);
      setSavedTrivia(newTrivia);
    }
  };

  const handleRefresh = () => {
    // Preconnect to the appropriate API before fetching
    if (category === 'joke') {
      preconnect('https://icanhazdadjoke.com');
    } else if (category === 'fact') {
      preconnect('https://uselessfacts.jsph.pl');
    } else if (category === 'riddle') {
      preconnect('https://riddles-api.vercel.app');
    } else if (category === 'trivia') {
      preconnect('https://the-trivia-api.com');
    }

    // Then fetch content
    fetchContent();
  };

  const [showSubmissionForm, setShowSubmissionForm] = useState(false);
  const [showUserContent, setShowUserContent] = useState(false);
  const [userSubmittedContent, setUserSubmittedContent] = useState([]);
  const [loadingUserContent, setLoadingUserContent] = useState(false);

  const fetchUserContent = async () => {
    setLoadingUserContent(true);
    const content = await fetchApprovedContent(category);
    setUserSubmittedContent(content);
    setLoadingUserContent(false);
    setShowUserContent(true);
  };

  const handleFooterNavigate = (page) => {
    navigate(`/${page}`);
  };

  return (
    <div className={`app ${darkMode ? 'dark-mode' : 'light-mode'}`}>
      <div className="theme-toggle">
        <button onClick={toggleTheme} className="theme-button">
          {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
        </button>
      </div>
      <div className="logo-container">
        <img
          src={darkMode ? logoDark : logoLight}
          alt="The Grin Bin"
          className="main-logo"
          onClick={() => navigate('/')}
          style={{ cursor: 'pointer' }}
        />
      </div>

      <div className="category-buttons">
        <button
          className={category === 'joke' ? 'active' : ''}
          onClick={() => {
            setCategory('joke');
            setNotepadTab('joke');
          }}
        >
          Dad Jokes
        </button>

        <button
          className={category === 'fact' ? 'active' : ''}
          onClick={() => {
            setCategory('fact');
            setNotepadTab('fact');
          }}
        >
          Random Facts
        </button>

        <button
          className={category === 'riddle' ? 'active' : ''}
          onClick={() => {
            setCategory('riddle');
            setNotepadTab('riddle');
          }}
        >
          Riddles
        </button>

        <button
          className={category === 'trivia' ? 'active' : ''}
          onClick={() => {
            setCategory('trivia');
            setNotepadTab('trivia');
          }}
        >
          Trivia
        </button>
      </div>

      {/* Move these buttons above the content container */}
      <div className="app-actions top-actions">
        <button
          className="submit-button"
          onClick={() => setShowSubmissionForm(true)}
        >
          <AddCircleOutlineIcon style={{ fontSize: '1rem', marginRight: '0.3rem' }} />
          Submit Your Own
        </button>

        <button
          className="user-content-button"
          onClick={fetchUserContent}
          disabled={loadingUserContent}
        >
          <PeopleOutlineIcon style={{ fontSize: '1rem', marginRight: '0.3rem' }} />
          Community Content
        </button>
      </div>

      {/* Add inline style to adjust height based on category with transition */}
      <div className="content-container" style={{
        height: category === 'trivia' ? '360px' : category === 'riddle' ? '280px' : '150px',
        transition: 'height 0.3s ease-in-out'
      }}>
        <div className="card-wrapper">
          <div className="card-transition">
            {category === 'joke' && content && (
              <JokeCard text={content} onSave={saveToNotepad} />
            )}
            {category === 'fact' && content && (
              <FactCard text={content} onSave={saveToNotepad} />
            )}
            {category === 'riddle' && riddleQuestion && (
              <RiddleCard
                question={riddleQuestion}
                answer={riddleAnswer}
                onSave={saveToNotepad}
              />
            )}
            {category === 'trivia' && triviaQuestion && (
              <TriviaCard
                question={triviaQuestion}
                correctAnswer={triviaCorrectAnswer}
                incorrectAnswers={triviaIncorrectAnswers}
                onSave={saveToNotepad}
              />
            )}
            {!content && <div className="empty-card"></div>}
          </div>
        </div>
      </div>

      <div className="app-actions">
        <button
          className="refresh-button"
          onClick={handleRefresh}
          disabled={loading}
        >
          <RefreshIcon style={{ marginRight: '0.5rem' }} /> Get Another
        </button>
      </div>

      <Notepad
        savedJokes={savedJokes}
        savedFacts={savedFacts}
        savedTrivia={savedTrivia}
        savedRiddles={savedRiddles}
        notepadTab={notepadTab}
        setNotepadTab={setNotepadTab}
        copyNotepad={copyNotepad}
        clearJokes={clearJokes}
        clearFacts={clearFacts}
        clearTrivia={clearTrivia}
        clearRiddles={clearRiddles}
        removeItem={removeItem}
        undoRemove={undoRemove}
      />

      {showSubmissionForm && (
        <div className="modal-overlay" onClick={(e) => {
          if (e.target.className === 'modal-overlay') {
            setShowSubmissionForm(false);
          }
        }}>
          <div className="modal-content">
            <SubmissionForm
              onClose={() => setShowSubmissionForm(false)}
              category={category}
            />
          </div>
        </div>
      )}

      {showUserContent && (
        <div className="modal-overlay" onClick={(e) => {
          if (e.target.className === 'modal-overlay') {
            setShowUserContent(false);
          }
        }}>
          <div className="modal-content">
            <button className="close-button" onClick={() => setShowUserContent(false)}>
              <CloseIcon />
            </button>
            <h2>Community Content</h2>

            {loadingUserContent ? (
              <p className="loading-message">Loading community content...</p>
            ) : userSubmittedContent.length > 0 ? (
              <div className="user-content-list">
                {userSubmittedContent.map(item => (
                  <div key={item.id} className="user-content-item">
                    <div className="content-main">
                      <div className="content-text">
                        {category === 'joke' || category === 'fact' ? (
                          <p>{item.content}</p>
                        ) : category === 'riddle' ? (
                          <>
                            <p><strong>Q:</strong> {item.content.question}</p>
                            <p><strong>A:</strong> {item.content.answer}</p>
                          </>
                        ) : (
                          <>
                            <p><strong>Q:</strong> {item.content.question}</p>
                            <p><strong>A:</strong> {item.content.correctAnswer}</p>
                          </>
                        )}
                        <p className="content-author">Submitted by: {item.authorName}</p>
                      </div>
                      <VotingButtons
                        contentId={item.id}
                        initialUpvotes={item.upvotes || 0}
                        initialDownvotes={item.downvotes || 0}
                        initialNetVotes={item.netVotes || 0}
                        onVoteUpdate={(contentId, newNetVotes) => {
                          // Update the local state to reflect vote changes
                          setUserSubmittedContent(prev =>
                            prev.map(content =>
                              content.id === contentId
                                ? { ...content, netVotes: newNetVotes }
                                : content
                            ).sort((a, b) => (b.netVotes || 0) - (a.netVotes || 0))
                          );
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="no-content-message">No community content available yet. Be the first to submit!</p>
            )}
          </div>
        </div>
      )}

      <Footer onNavigate={handleFooterNavigate} />
      <CookieConsent />
    </div>
  );
};

export default WordPlay;
