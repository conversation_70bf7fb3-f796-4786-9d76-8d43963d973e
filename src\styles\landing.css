.landing-container {
  max-width: 1200px;
  width: 100%;
  padding: 2rem;
  text-align: center;
}

.landing-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-light);
  font-family: "<PERSON><PERSON>", sans-serif;
}

.dark-mode .landing-title {
  color: var(--text-dark);
}

.landing-subtitle {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  color: #666;
  font-weight: 400;
}

.dark-mode .landing-subtitle {
  color: #aaa;
}

.section-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.section-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.section-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(13, 71, 161, 0.05) 0%, rgba(13, 71, 161, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.section-card:hover::before {
  opacity: 1;
}

.section-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  border-color: var(--text-light);
}

.dark-mode .section-card {
  background: #2d2d2d;
  border-color: transparent;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark-mode .section-card::before {
  background: linear-gradient(135deg, rgba(227, 242, 253, 0.05) 0%, rgba(227, 242, 253, 0.1) 100%);
}

.dark-mode .section-card:hover {
  border-color: var(--text-dark);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.4);
}

.card-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
}

.section-card h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-light);
  position: relative;
  z-index: 1;
}

.dark-mode .section-card h2 {
  color: var(--text-dark);
}

.section-card p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: #666;
  position: relative;
  z-index: 1;
}

.dark-mode .section-card p {
  color: #aaa;
}

.card-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.card-features span {
  background: rgba(13, 71, 161, 0.1);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.dark-mode .card-features span {
  background: rgba(227, 242, 253, 0.1);
  color: var(--text-dark);
}

.card-button {
  background: linear-gradient(135deg, var(--text-light) 0%, #1565c0 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  font-family: "Josefin Sans", sans-serif;
}

.card-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(13, 71, 161, 0.3);
}

.dark-mode .card-button {
  background: linear-gradient(135deg, var(--text-dark) 0%, #90caf9 100%);
  color: var(--bg-dark);
}

.dark-mode .card-button:hover {
  box-shadow: 0 8px 24px rgba(227, 242, 253, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing-container {
    padding: 1rem;
  }

  .landing-title {
    font-size: 2.5rem;
  }

  .landing-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .section-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .section-card {
    padding: 2rem;
  }

  .card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .section-card h2 {
    font-size: 1.5rem;
  }

  .section-card p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .card-features {
    margin-bottom: 1.5rem;
  }

  .card-features span {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .card-button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .landing-title {
    font-size: 2rem;
  }

  .section-card {
    padding: 1.5rem;
  }

  .card-icon {
    font-size: 2.5rem;
  }

  .section-card h2 {
    font-size: 1.3rem;
  }

  .card-features {
    flex-direction: column;
    align-items: center;
  }

  .card-features span {
    font-size: 0.75rem;
  }
}
