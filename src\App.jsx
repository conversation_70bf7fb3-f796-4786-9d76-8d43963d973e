import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import About from './pages/About';
import CommunityGuidelines from './pages/CommunityGuidelines';
import DMCAPolicy from './pages/DMCAPolicy';
import Contact from './pages/Contact';
import LandingPage from './pages/LandingPage';
import WordPlay from './pages/WordPlay';
import ClassicGames from './pages/ClassicGames';

import './styles/app.css';

// Main App component with routing
function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/wordplay" element={<WordPlay />} />
        <Route path="/classic-games" element={<ClassicGames />} />
        <Route path="/privacy" element={<PrivacyPolicy />} />
        <Route path="/terms" element={<TermsOfService />} />
        <Route path="/about" element={<About />} />
        <Route path="/guidelines" element={<CommunityGuidelines />} />
        <Route path="/dmca" element={<DMCAPolicy />} />
        <Route path="/contact" element={<Contact />} />
      </Routes>
    </Router>
  );
}

export default App;
